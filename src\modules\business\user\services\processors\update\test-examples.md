# Test Examples cho Image Processing

## 🧪 V<PERSON> Dụ Test Requests

### 1. Test imagesMediaTypes (Thay thế toàn bộ)

```bash
curl -X PUT http://localhost:3000/business/user/products/123 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Sản phẩm test",
    "imagesMediaTypes": ["image/jpeg", "image/png", "image/webp"]
  }'
```

**Expected Response:**
```json
{
  "product": {
    "id": 123,
    "name": "Sản phẩm test",
    "images": [
      "business/IMAGE/2025/06/product-image-0-1234567890.jpg",
      "business/IMAGE/2025/06/product-image-1-1234567890.png", 
      "business/IMAGE/2025/06/product-image-2-1234567890.webp"
    ]
  },
  "imagesUploadUrls": [
    {
      "url": "https://presigned-url-1...",
      "key": "business/IMAGE/2025/06/product-image-0-1234567890.jpg",
      "type": "image/jpeg",
      "position": 0
    },
    {
      "url": "https://presigned-url-2...",
      "key": "business/IMAGE/2025/06/product-image-1-1234567890.png",
      "type": "image/png", 
      "position": 1
    },
    {
      "url": "https://presigned-url-3...",
      "key": "business/IMAGE/2025/06/product-image-2-1234567890.webp",
      "type": "image/webp",
      "position": 2
    }
  ]
}
```

### 2. Test imageOperations (ADD/DELETE)

```bash
curl -X PUT http://localhost:3000/business/user/products/123 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Sản phẩm test operations",
    "imageOperations": [
      {
        "operation": "DELETE",
        "key": "business/IMAGE/2025/06/old-image.jpg"
      },
      {
        "operation": "ADD",
        "mimeType": "image/jpeg"
      },
      {
        "operation": "ADD", 
        "mimeType": "image/png"
      }
    ]
  }'
```

**Expected Response:**
```json
{
  "product": {
    "id": 123,
    "name": "Sản phẩm test operations",
    "images": [
      "business/IMAGE/2025/06/remaining-image.png",
      "business/IMAGE/2025/06/product-image-1-1234567890.jpg",
      "business/IMAGE/2025/06/product-image-2-1234567890.png"
    ]
  },
  "imagesUploadUrls": [
    {
      "url": "https://presigned-url-new-1...",
      "key": "business/IMAGE/2025/06/product-image-1-1234567890.jpg",
      "type": "image/jpeg",
      "position": 1
    },
    {
      "url": "https://presigned-url-new-2...",
      "key": "business/IMAGE/2025/06/product-image-2-1234567890.png",
      "type": "image/png",
      "position": 2
    }
  ]
}
```

### 3. Test Deprecated images

```bash
curl -X PUT http://localhost:3000/business/user/products/123 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Sản phẩm test deprecated",
    "images": [
      {
        "operation": "DELETE",
        "position": 0
      },
      {
        "operation": "ADD",
        "mimeType": "image/gif"
      }
    ]
  }'
```

## 🔍 Debug & Logging

Khi chạy test, check logs để verify:

```
[UpdateProductOrchestrator] Processing 3 images via imagesMediaTypes for product 123
[UpdateProductOrchestrator] Successfully processed 3 images via imagesMediaTypes

[UpdateProductOrchestrator] Processing 3 image operations for product 123
[UpdateProductOrchestrator] Deleted image with key: business/IMAGE/2025/06/old-image.jpg
[UpdateProductOrchestrator] Added new image at position 1 with key: business/IMAGE/2025/06/product-image-1-1234567890.jpg
[UpdateProductOrchestrator] Added new image at position 2 with key: business/IMAGE/2025/06/product-image-2-1234567890.png
[UpdateProductOrchestrator] Successfully processed 3 image operations
```

## ⚠️ Error Cases

### 1. Invalid MIME Type

```json
{
  "imageOperations": [
    {
      "operation": "ADD",
      "mimeType": "invalid/type"
    }
  ]
}
```

**Expected:** Fallback to `image/jpeg`

### 2. Missing Key for DELETE

```json
{
  "imageOperations": [
    {
      "operation": "DELETE"
    }
  ]
}
```

**Expected:** Skip operation, log warning

### 3. S3 Service Error

**Expected Response:**
```json
{
  "error": "PRODUCT_UPDATE_FAILED",
  "message": "Lỗi khi xử lý hình ảnh sản phẩm: S3 connection failed"
}
```

## 🎯 Frontend Integration

Sau khi nhận response, frontend cần:

1. **Upload files** lên các presigned URLs
2. **Verify upload** thành công
3. **Update UI** với ảnh mới

```javascript
// Frontend example
const response = await updateProduct(productId, {
  imagesMediaTypes: ['image/jpeg', 'image/png']
});

// Upload files to presigned URLs
for (const uploadUrl of response.imagesUploadUrls) {
  await fetch(uploadUrl.url, {
    method: 'PUT',
    body: imageFile,
    headers: {
      'Content-Type': uploadUrl.type
    }
  });
}
```

## 📊 Performance Testing

Test với nhiều ảnh:

```json
{
  "imagesMediaTypes": [
    "image/jpeg", "image/png", "image/webp", "image/gif",
    "image/jpeg", "image/png", "image/webp", "image/gif",
    "image/jpeg", "image/png"
  ]
}
```

**Expected:** Tạo 10 presigned URLs trong < 2 seconds

## 🔧 Troubleshooting

### Issue: "S3Service not found"
**Solution:** Check BusinessUserModule imports ServicesModule

### Issue: "Invalid S3 key"
**Solution:** Check generateS3Key parameters

### Issue: "Presigned URL expired"
**Solution:** URLs có hiệu lực 15 phút, tạo mới nếu cần

### Issue: "Product.images not updated"
**Solution:** Check transaction rollback, verify save operation
