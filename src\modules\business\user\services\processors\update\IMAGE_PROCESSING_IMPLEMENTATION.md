# Triển Khai Xử Lý Hình Ảnh cho UpdateProduct

## 📋 Tổng Quan

Đã triển khai hoàn chỉnh logic xử lý hình ảnh trong `UpdateProductOrchestrator` với 3 cách xử lý khác nhau:

1. **`imagesMediaTypes`** - Thay thế toàn bộ ảnh (cách mới nhất)
2. **`imageOperations`** - Thao tác ADD/DELETE từng ảnh cụ thể
3. **`images`** - Deprecated (tương thích ngược)

## 🔧 Implementation Details

### 1. Method Chính: `processMainProductImages`

```typescript
private async processMainProductImages(
  updateProductDto: BusinessUpdateProductDto,
  product: UserProduct,
  timestamp: number,
): Promise<unknown[]>
```

**Chức năng:**
- Xử lý 3 cách upload ảnh theo thứ tự ưu tiên
- <PERSON><PERSON><PERSON> presigned URLs cho S3 upload
- Cập nhật `product.images` array
- Error handling và logging chi tiết

### 2. Method Helper: `processImageOperations`

**Xử lý imageOperations (ADD/DELETE):**
- DELETE: Xóa ảnh theo `key` hoặc `position`
- ADD: Tạo S3 key mới và presigned URL
- Tự động tính toán position cho ảnh mới

### 3. Method Helper: `processDeprecatedImages`

**Xử lý images (deprecated):**
- Tương tự `processImageOperations` nhưng cho format cũ
- Đảm bảo tương thích ngược

## 📊 Luồng Xử Lý

```
UpdateProduct Request
        ↓
1. Validate Product
        ↓
2. Update Basic Fields
        ↓
3. Update Pricing
        ↓
4. Process Main Images ← [TRIỂN KHAI MỚI]
   ├── imagesMediaTypes (thay thế toàn bộ)
   ├── imageOperations (ADD/DELETE)
   └── images (deprecated)
        ↓
5. Update Custom Fields
        ↓
6. Process Product Type Specific
        ↓
7. Process Classifications
        ↓
8. Build Response
```

## 🎯 Cách Sử Dụng

### 1. Thay Thế Toàn Bộ Ảnh

```json
{
  "imagesMediaTypes": ["image/jpeg", "image/png", "image/webp"]
}
```

**Kết quả:**
- Xóa tất cả ảnh cũ
- Tạo 3 presigned URLs mới
- Cập nhật `product.images` với 3 keys mới

### 2. Thao Tác Chi Tiết

```json
{
  "imageOperations": [
    {
      "operation": "DELETE",
      "key": "business/IMAGE/2025/06/old-image.jpg"
    },
    {
      "operation": "ADD",
      "mimeType": "image/jpeg"
    }
  ]
}
```

**Kết quả:**
- Xóa ảnh có key cụ thể
- Thêm 1 presigned URL mới

### 3. Deprecated (Tương Thích Ngược)

```json
{
  "images": [
    {
      "operation": "DELETE",
      "position": 0
    },
    {
      "operation": "ADD",
      "mimeType": "image/png"
    }
  ]
}
```

## 📝 Response Format

```json
{
  "product": { /* Product data */ },
  "imagesUploadUrls": [
    {
      "url": "https://presigned-url...",
      "key": "business/IMAGE/2025/06/product-image-0-1234567890.jpg",
      "type": "image/jpeg",
      "position": 0
    }
  ]
}
```

## ⚙️ Cấu Hình S3

**S3 Key Pattern:**
```
business/IMAGE/{year}/{month}/product-image-{position}-{timestamp}
```

**Presigned URL Settings:**
- Thời gian hết hạn: 15 phút
- Kích thước tối đa: 5MB
- Loại file hỗ trợ: JPEG, PNG, WEBP, GIF

## 🔍 Error Handling

- **Invalid MIME Type**: Fallback về JPEG
- **S3 Service Error**: Throw AppException với PRODUCT_UPDATE_FAILED
- **Missing Parameters**: Log warning và skip operation
- **Type Conversion**: Explicit casting cho ProductImagesType

## 📈 Performance

- **Async Processing**: Tất cả S3 calls đều async
- **Batch Operations**: Xử lý multiple images trong 1 request
- **Memory Efficient**: Không load image content, chỉ tạo URLs

## 🧪 Testing

Để test implementation:

1. **Unit Test**: Mock S3Service và test từng method
2. **Integration Test**: Test với real S3 service
3. **API Test**: Test qua endpoint `/business/user/products/{id}`

## 🚀 Deployment

Implementation đã sẵn sàng sử dụng:
- ✅ Dependencies đã được inject
- ✅ Module configuration đã đúng
- ✅ Error handling hoàn chỉnh
- ✅ Logging chi tiết
- ✅ TypeScript types đã fix

## 📚 Related Files

- `update-product-orchestrator.ts` - Main implementation
- `BusinessUpdateProductDto` - Request DTO
- `ImageOperationDto` - Image operation DTO
- `UserProduct.images` - Database field
- `S3Service` - Cloud storage service
