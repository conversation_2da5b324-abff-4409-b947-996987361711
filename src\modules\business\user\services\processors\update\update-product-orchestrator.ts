import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  BusinessUpdateProductDto,
  BusinessProductResponseDto as ProductResponseDto,
  ImageOperationDto,
} from '../../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ProductTypeEnum } from '@modules/business/enums';
import { UserProduct } from '@modules/business/entities';
import { UpdateProductProcessor } from './update-product.processor';
import { PhysicalProductUpdateProcessor } from './physical-product-update.processor';
import { DigitalProductUpdateProcessor } from './digital-product-update.processor';
import { EventProductUpdateProcessor } from './event-product-update.processor';
import { ServiceProductUpdateProcessor } from './service-product-update.processor';
import { ComboProductUpdateProcessor } from './combo-product-update.processor';
import { InventoryResponseDto } from '../../../dto/inventory';
import { ClassificationResponseDto } from '../../../dto';

// Import S3 và utilities
import { S3Service } from '@shared/services/s3.service';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { FileSizeEnum, ImageTypeEnum, TimeIntervalEnum } from '@shared/utils';

/**
 * Orchestrator chính cho việc cập nhật sản phẩm
 * Điều phối toàn bộ luồng update từ các processor theo loại sản phẩm
 */
@Injectable()
export class UpdateProductOrchestrator {
  private readonly logger = new Logger(UpdateProductOrchestrator.name);

  constructor(
    private readonly updateProcessor: UpdateProductProcessor,
    private readonly physicalProductUpdateProcessor: PhysicalProductUpdateProcessor,
    private readonly digitalProductUpdateProcessor: DigitalProductUpdateProcessor,
    private readonly eventProductUpdateProcessor: EventProductUpdateProcessor,
    private readonly serviceProductUpdateProcessor: ServiceProductUpdateProcessor,
    private readonly comboProductUpdateProcessor: ComboProductUpdateProcessor,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Method chính để cập nhật sản phẩm
   * Sử dụng processors chuyên biệt theo loại sản phẩm
   */
  @Transactional()
  async updateProduct(
    id: number,
    updateProductDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    try {
      this.logger.log(
        `Bắt đầu cập nhật sản phẩm ID: ${id} cho user: ${userId}`,
      );

      // BƯỚC 1: Tìm và validate sản phẩm
      const product = await this.updateProcessor.findAndValidateProduct(
        id,
        userId,
      );

      // BƯỚC 2: Cập nhật thông tin cơ bản (chung cho tất cả loại sản phẩm)
      this.updateProcessor.updateBasicFields(product, updateProductDto);

      // BƯỚC 3: Cập nhật giá sản phẩm (chung cho tất cả loại sản phẩm)
      this.updateProcessor.updateProductPricing(product, updateProductDto);

      // BƯỚC 4: Xử lý hình ảnh sản phẩm chính
      const imagesUploadUrls = await this.processMainProductImages(updateProductDto, product, Date.now());

      // BƯỚC 5: Cập nhật custom fields và metadata (chung)
      await this.updateProcessor.updateCustomFields(product, updateProductDto);

      // BƯỚC 6: Xử lý cập nhật theo loại sản phẩm cụ thể
      const productSpecificResult = await this.processProductTypeSpecificUpdate(
        product,
        updateProductDto,
        userId,
      );

      // BƯỚC 7: Xử lý classifications (chung cho tất cả loại sản phẩm)
      const { classifications, classificationUploadUrls } =
        await this.updateProcessor.processClassificationsUpdate(
          id,
          updateProductDto,
          userId,
        );

      // BƯỚC 8: Xử lý xóa classifications
      await this.updateProcessor.processClassificationsDeletion(
        id,
        updateProductDto.classificationsToDelete || [],
        userId,
      );

      // BƯỚC 9: Xây dựng response cuối cùng
      const response = await this.updateProcessor.buildUpdateResponse(
        productSpecificResult.product,
        imagesUploadUrls,
        productSpecificResult.advancedImagesUploadUrls,
        classificationUploadUrls,
        classifications,
        productSpecificResult.inventory,
      );

      this.logger.log(
        `Hoàn thành cập nhật sản phẩm ID: ${id} (${product.productType})`,
      );
      return response;
    } catch (error) {
      const errorStack = (error as Error).stack;
      this.logger.error(`Lỗi khi cập nhật sản phẩm ID: ${id}`, errorStack);

      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Wrap lỗi khác thành AppException
      const errorMessage = (error as Error).message;
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm: ${errorMessage}`,
      );
    }
  }

  /**
   * Xử lý cập nhật theo loại sản phẩm cụ thể
   */
  private async processProductTypeSpecificUpdate(
    product: UserProduct,
    updateProductDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(
      `Processing ${product.productType} specific update for product ${product.id}`,
    );

    switch (product.productType) {
      case ProductTypeEnum.PHYSICAL:
        return await this.physicalProductUpdateProcessor.updatePhysicalProduct(
          product,
          updateProductDto,
          userId,
        );

      case ProductTypeEnum.DIGITAL:
        return await this.digitalProductUpdateProcessor.updateDigitalProduct(
          product,
          updateProductDto,
          userId,
        );

      case ProductTypeEnum.EVENT:
        return await this.eventProductUpdateProcessor.updateEventProduct(
          product,
          updateProductDto,
          userId,
        );

      case ProductTypeEnum.SERVICE:
        return await this.serviceProductUpdateProcessor.updateServiceProduct(
          product,
          updateProductDto,
          userId,
        );

      case ProductTypeEnum.COMBO:
        return await this.comboProductUpdateProcessor.updateComboProduct(
          product,
          updateProductDto,
          userId,
        );

      default:
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Loại sản phẩm không được hỗ trợ: ${String(product.productType)}`,
        );
    }
  }

  /**
   * Xử lý hình ảnh sản phẩm chính
   * Hỗ trợ 3 cách xử lý: imagesMediaTypes, imageOperations, images (deprecated)
   */
  private async processMainProductImages(
    updateProductDto: BusinessUpdateProductDto,
    product: UserProduct,
    timestamp: number,
  ): Promise<unknown[]> {
    const imagesUploadUrls: unknown[] = [];

    try {
      // Cách 1: Xử lý imagesMediaTypes (thay thế toàn bộ ảnh)
      if (updateProductDto.imagesMediaTypes && updateProductDto.imagesMediaTypes.length > 0) {
        this.logger.log(
          `Processing ${updateProductDto.imagesMediaTypes.length} images via imagesMediaTypes for product ${product.id}`,
        );

        const imageEntries: string[] = [];

        for (let i = 0; i < updateProductDto.imagesMediaTypes.length; i++) {
          const mediaType = updateProductDto.imagesMediaTypes[i];

          // Tạo S3 key cho hình ảnh
          const imageKey = generateS3Key({
            baseFolder: 'business',
            categoryFolder: CategoryFolderEnum.IMAGE,
            fileName: `product-image-${i}-${timestamp}`,
            useTimeFolder: true,
          });

          // Thêm vào danh sách images
          imageEntries.push(imageKey);

          // Tạo presigned URL cho upload
          const uploadUrl = await this.s3Service.createPresignedWithID(
            imageKey,
            TimeIntervalEnum.FIFTEEN_MINUTES,
            ImageTypeEnum[mediaType as keyof typeof ImageTypeEnum] || ImageTypeEnum.JPEG,
            FileSizeEnum.FIVE_MB,
          );

          imagesUploadUrls.push({
            url: uploadUrl,
            key: imageKey,
            type: mediaType,
            position: i,
          });
        }

        // Cập nhật product images (thay thế toàn bộ)
        product.images = imageEntries;

        this.logger.log(`Successfully processed ${imageEntries.length} images via imagesMediaTypes`);
        return imagesUploadUrls;
      }

      // Cách 2: Xử lý imageOperations (ADD/DELETE cụ thể)
      if (updateProductDto.imageOperations && updateProductDto.imageOperations.length > 0) {
        this.logger.log(
          `Processing ${updateProductDto.imageOperations.length} image operations for product ${product.id}`,
        );

        await this.processImageOperations(updateProductDto.imageOperations, product, imagesUploadUrls, timestamp);

        this.logger.log(`Successfully processed ${updateProductDto.imageOperations.length} image operations`);
        return imagesUploadUrls;
      }

      // Cách 3: Xử lý images (deprecated - tương thích ngược)
      if (updateProductDto.images && updateProductDto.images.length > 0) {
        this.logger.log(
          `Processing ${updateProductDto.images.length} images via deprecated images field for product ${product.id}`,
        );

        await this.processDeprecatedImages(updateProductDto.images, product, imagesUploadUrls, timestamp);

        this.logger.log(`Successfully processed ${updateProductDto.images.length} deprecated images`);
        return imagesUploadUrls;
      }

      // Không có hình ảnh nào để xử lý
      this.logger.log(`No images to process for product ${product.id}`);
      return imagesUploadUrls;

    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Error processing main product images for product ${product.id}: ${errorMessage}`,
        errorStack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi xử lý hình ảnh sản phẩm: ${errorMessage}`,
      );
    }
  }

  /**
   * Xử lý imageOperations (ADD/DELETE cụ thể)
   */
  private async processImageOperations(
    imageOperations: ImageOperationDto[],
    product: UserProduct,
    imagesUploadUrls: unknown[],
    timestamp: number,
  ): Promise<void> {
    // Đảm bảo product.images là mảng
    if (!Array.isArray(product.images)) {
      product.images = [];
    }

    // Xử lý các thao tác DELETE trước
    const deleteOperations = imageOperations.filter(op => op.operation === 'DELETE');
    for (const deleteOp of deleteOperations) {
      if (deleteOp.key) {
        try {
          // Xóa file trên S3
          await this.s3Service.deleteFile(deleteOp.key);
          this.logger.log(`Successfully deleted file from S3: ${deleteOp.key}`);
        } catch (error) {
          const errorMessage = (error as Error).message;
          this.logger.warn(`Failed to delete file from S3: ${deleteOp.key}, error: ${errorMessage}`);
          // Tiếp tục xử lý mặc dù xóa S3 thất bại
        }

        // Xóa khỏi database
        product.images = (product.images as any[]).filter((img: any) => {
          if (typeof img === 'string') {
            return img !== deleteOp.key;
          }
          return img.key !== deleteOp.key;
        });
        this.logger.log(`Removed image key from database: ${deleteOp.key}`);
      } else if (deleteOp.position !== undefined) {
        // Xóa theo position (lấy key trước khi xóa để xóa trên S3)
        const currentImages = product.images as any[];
        if (deleteOp.position < currentImages.length) {
          const imageToDelete = currentImages[deleteOp.position];
          const keyToDelete = typeof imageToDelete === 'string' ? imageToDelete : imageToDelete?.key;

          if (keyToDelete) {
            try {
              // Xóa file trên S3
              await this.s3Service.deleteFile(keyToDelete);
              this.logger.log(`Successfully deleted file from S3: ${keyToDelete}`);
            } catch (error) {
              const errorMessage = (error as Error).message;
              this.logger.warn(`Failed to delete file from S3: ${keyToDelete}, error: ${errorMessage}`);
            }
          }
        }

        // Xóa khỏi database theo position
        product.images = (product.images as any[]).filter((_: any, index: number) => index !== deleteOp.position);
        this.logger.log(`Removed image at position ${deleteOp.position} from database`);
      }
    }

    // Xử lý các thao tác ADD
    const addOperations = imageOperations.filter(op => op.operation === 'ADD');
    for (const addOp of addOperations) {
      if (addOp.mimeType) {
        try {
          // Tìm vị trí lớn nhất hiện tại và tăng lên 1
          const currentImages = product.images as any[];
          const maxPosition = currentImages.length > 0
            ? Math.max(...currentImages.map((_: any, index: number) => index))
            : -1;
          const newPosition = maxPosition + 1;

          // Tạo S3 key cho hình ảnh mới
          const imageKey = generateS3Key({
            baseFolder: 'business',
            categoryFolder: CategoryFolderEnum.IMAGE,
            fileName: `product-image-${newPosition}-${timestamp}`,
            useTimeFolder: true,
          });

          // Thêm vào danh sách images
          (product.images as any[]).push(imageKey);

          // Tạo presigned URL cho upload
          const uploadUrl = await this.s3Service.createPresignedWithID(
            imageKey,
            TimeIntervalEnum.FIFTEEN_MINUTES,
            ImageTypeEnum[addOp.mimeType as keyof typeof ImageTypeEnum] || ImageTypeEnum.JPEG,
            FileSizeEnum.FIVE_MB,
          );

          imagesUploadUrls.push({
            url: uploadUrl,
            key: imageKey,
            type: addOp.mimeType,
            position: newPosition,
          });

          this.logger.log(`Added new image at position ${newPosition} with key: ${imageKey}`);
        } catch (error) {
          const errorMessage = (error as Error).message;
          this.logger.error(`Failed to process ADD operation: ${errorMessage}`);
          throw error;
        }
      }
    }
  }

  /**
   * Xử lý images (deprecated - tương thích ngược)
   */
  private async processDeprecatedImages(
    images: any[],
    product: UserProduct,
    imagesUploadUrls: unknown[],
    timestamp: number,
  ): Promise<void> {
    // Đảm bảo product.images là mảng
    if (!Array.isArray(product.images)) {
      product.images = [];
    }

    // Xử lý các thao tác DELETE trước
    const deleteOperations = images.filter((img: any) => img.operation === 'DELETE');
    for (const deleteOp of deleteOperations) {
      if (deleteOp.key) {
        try {
          // Xóa file trên S3
          await this.s3Service.deleteFile(deleteOp.key);
          this.logger.log(`Successfully deleted deprecated file from S3: ${deleteOp.key}`);
        } catch (error) {
          const errorMessage = (error as Error).message;
          this.logger.warn(`Failed to delete deprecated file from S3: ${deleteOp.key}, error: ${errorMessage}`);
          // Tiếp tục xử lý mặc dù xóa S3 thất bại
        }

        // Xóa khỏi database
        product.images = (product.images as any[]).filter((img: any) => {
          if (typeof img === 'string') {
            return img !== deleteOp.key;
          }
          return img.key !== deleteOp.key;
        });
        this.logger.log(`Removed deprecated image key from database: ${deleteOp.key}`);
      } else if (deleteOp.position !== undefined) {
        // Xóa theo position (lấy key trước khi xóa để xóa trên S3)
        const currentImages = product.images as any[];
        if (deleteOp.position < currentImages.length) {
          const imageToDelete = currentImages[deleteOp.position];
          const keyToDelete = typeof imageToDelete === 'string' ? imageToDelete : imageToDelete?.key;

          if (keyToDelete) {
            try {
              // Xóa file trên S3
              await this.s3Service.deleteFile(keyToDelete);
              this.logger.log(`Successfully deleted deprecated file from S3: ${keyToDelete}`);
            } catch (error) {
              const errorMessage = (error as Error).message;
              this.logger.warn(`Failed to delete deprecated file from S3: ${keyToDelete}, error: ${errorMessage}`);
            }
          }
        }

        // Xóa khỏi database theo position
        product.images = (product.images as any[]).filter((_: any, index: number) => index !== deleteOp.position);
        this.logger.log(`Removed deprecated image at position ${deleteOp.position} from database`);
      }
    }

    // Xử lý các thao tác ADD
    const addOperations = images.filter((img: any) => img.operation === 'ADD');
    for (const addOp of addOperations) {
      if (addOp.mimeType) {
        try {
          // Tìm vị trí lớn nhất hiện tại và tăng lên 1
          const currentImages = product.images as any[];
          const maxPosition = currentImages.length > 0
            ? Math.max(...currentImages.map((_: any, index: number) => index))
            : -1;
          const newPosition = maxPosition + 1;

          // Tạo S3 key cho hình ảnh mới
          const imageKey = generateS3Key({
            baseFolder: 'business',
            categoryFolder: CategoryFolderEnum.IMAGE,
            fileName: `product-image-deprecated-${newPosition}-${timestamp}`,
            useTimeFolder: true,
          });

          // Thêm vào danh sách images
          (product.images as any[]).push(imageKey);

          // Tạo presigned URL cho upload
          const uploadUrl = await this.s3Service.createPresignedWithID(
            imageKey,
            TimeIntervalEnum.FIFTEEN_MINUTES,
            ImageTypeEnum[addOp.mimeType as keyof typeof ImageTypeEnum] || ImageTypeEnum.JPEG,
            FileSizeEnum.FIVE_MB,
          );

          imagesUploadUrls.push({
            url: uploadUrl,
            key: imageKey,
            type: addOp.mimeType,
            position: newPosition,
          });

          this.logger.log(`Added new deprecated image at position ${newPosition} with key: ${imageKey}`);
        } catch (error) {
          const errorMessage = (error as Error).message;
          this.logger.error(`Failed to process deprecated ADD operation: ${errorMessage}`);
          throw error;
        }
      }
    }
  }

  /**
   * Xử lý image operations cho advanced info khi không có advancedInfo trong request
   */
  private processAdvancedImageOperations(
    product: Record<string, unknown>,
    updateProductDto: BusinessUpdateProductDto,
  ): void {
    // Xử lý imageOperations cho ticket types/service packages
    if (this.shouldProcessAdvancedImageOperations(product, updateProductDto)) {
      try {
        this.logger.log(
          `Xử lý imageOperations cho ${String(product.productType)} product với ${updateProductDto.imageOperations?.length || 0} operations`,
        );

        // TODO: Implement logic từ service gốc
        // await this.processAdvancedImageOperations(...)
      } catch (imageOperationsError) {
        const errorMessage = (imageOperationsError as Error).message;
        this.logger.warn(
          `Không thể xử lý imageOperations cho sản phẩm ${String(product.id)}: ${errorMessage}`,
        );
      }
    }

    // Xử lý images (deprecated) cho ticket types/service packages
    if (this.shouldProcessAdvancedImages(product, updateProductDto)) {
      try {
        this.logger.log(
          `Xử lý images cho ${String(product.productType)} advanced info với ${updateProductDto.images?.length || 0} operations`,
        );

        // TODO: Implement logic từ service gốc
        // await this.processAdvancedImages(...)
      } catch (imagesError) {
        const errorMessage = (imagesError as Error).message;
        this.logger.warn(
          `Không thể xử lý images cho advanced info của sản phẩm ${String(product.id)}: ${errorMessage}`,
        );
      }
    }
  }

  /**
   * Kiểm tra có nên xử lý advanced image operations không
   */
  private shouldProcessAdvancedImageOperations(
    product: Record<string, unknown>,
    updateProductDto: BusinessUpdateProductDto,
  ): boolean {
    return Boolean(
      !updateProductDto.advancedInfo &&
        updateProductDto.imageOperations &&
        updateProductDto.imageOperations.length > 0 &&
        product.detail_id &&
        (product.productType === 'EVENT' || product.productType === 'SERVICE'),
    );
  }

  /**
   * Kiểm tra có nên xử lý advanced images không
   */
  private shouldProcessAdvancedImages(
    product: Record<string, unknown>,
    updateProductDto: BusinessUpdateProductDto,
  ): boolean {
    const isAdvancedImages = !(
      updateProductDto as unknown as Record<string, unknown>
    ).isProductImageOperations;

    return Boolean(
      !updateProductDto.advancedInfo &&
        updateProductDto.images &&
        updateProductDto.images.length > 0 &&
        product.detail_id &&
        (product.productType === 'EVENT' ||
          product.productType === 'SERVICE') &&
        isAdvancedImages,
    );
  }
}

/**
 * Interface cho kết quả xử lý update từ processors chuyên biệt
 */
export interface UpdateProductResult {
  product: UserProduct;
  imagesUploadUrls: string[];
  advancedImagesUploadUrls: string[];
  classificationUploadUrls: string[];
  classifications: ClassificationResponseDto[];
  inventory: InventoryResponseDto[] | null;
}

/**
 * Interface cho context update
 */
export interface UpdateProductContext {
  productId: number;
  userId: number;
  updateDto: BusinessUpdateProductDto;
  timestamp: number;
}

/**
 * Enum cho các bước update
 */
export enum UpdateStep {
  VALIDATE_PRODUCT = 'validate_product',
  UPDATE_BASIC_FIELDS = 'update_basic_fields',
  UPDATE_PRICING = 'update_pricing',
  PROCESS_IMAGES = 'process_images',
  UPDATE_CUSTOM_FIELDS = 'update_custom_fields',
  SAVE_PRODUCT = 'save_product',
  PROCESS_ADVANCED_INFO = 'process_advanced_info',
  PROCESS_CLASSIFICATIONS = 'process_classifications',
  PROCESS_INVENTORY = 'process_inventory',
  BUILD_RESPONSE = 'build_response',
}

/**
 * Interface cho tracking progress
 */
export interface UpdateProgress {
  currentStep: UpdateStep;
  completedSteps: UpdateStep[];
  totalSteps: number;
  errors: string[];
  warnings: string[];
}
